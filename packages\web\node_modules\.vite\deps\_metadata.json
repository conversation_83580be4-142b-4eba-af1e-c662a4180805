{"hash": "b899afdb", "browserHash": "334434b2", "optimized": {"react": {"src": "../../../../../node_modules/.pnpm/react@18.3.1/node_modules/react/index.js", "file": "react.js", "fileHash": "bb025500", "needsInterop": true}, "react-dom": {"src": "../../../../../node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "e6391c89", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../../node_modules/.pnpm/react@18.3.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b006f58e", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../../node_modules/.pnpm/react@18.3.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d8b61c29", "needsInterop": true}, "@radix-ui/react-select": {"src": "../../../../../node_modules/.pnpm/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "fff23405", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "8251ad90", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../../node_modules/.pnpm/@radix-ui+react-toast@1.2.1_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "a6b847d1", "needsInterop": false}, "class-variance-authority": {"src": "../../../../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "ca4ee143", "needsInterop": false}, "clsx": {"src": "../../../../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "87f51114", "needsInterop": false}, "framer-motion": {"src": "../../../../../node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "f82a6597", "needsInterop": false}, "lucide-react": {"src": "../../../../../node_modules/.pnpm/lucide-react@0.285.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "507a24d0", "needsInterop": false}, "react-chatbot-kit": {"src": "../../../../../node_modules/.pnpm/react-chatbot-kit@2.2.2/node_modules/react-chatbot-kit/build/index.js", "file": "react-chatbot-kit.js", "fileHash": "d4d2101a", "needsInterop": true}, "react-dom/client": {"src": "../../../../../node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1fd5c61d", "needsInterop": true}, "react-router-dom": {"src": "../../../../../node_modules/.pnpm/react-router-dom@6.30.1_rea_32183f923d2f77881bbb08a88c6f2afc/node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "d3b64830", "needsInterop": false}, "tailwind-merge": {"src": "../../../../../node_modules/.pnpm/tailwind-merge@1.14.0/node_modules/tailwind-merge/dist/tailwind-merge.mjs", "file": "tailwind-merge.js", "fileHash": "37d357e6", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../../node_modules/.pnpm/@radix-ui+react-dialog@1.1._979338a14129bfbd4b93c15b369f3450/node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "3deedb56", "needsInterop": false}}, "chunks": {"chunk-4TD3FT2H": {"file": "chunk-4TD3FT2H.js"}, "chunk-IRIUITFQ": {"file": "chunk-IRIUITFQ.js"}, "chunk-SO5BMGJS": {"file": "chunk-SO5BMGJS.js"}, "chunk-6PKV4LPG": {"file": "chunk-6PKV4LPG.js"}, "chunk-F5E2Y2MB": {"file": "chunk-F5E2Y2MB.js"}, "chunk-S2NUOSNO": {"file": "chunk-S2NUOSNO.js"}, "chunk-H4IBFD3B": {"file": "chunk-H4IBFD3B.js"}, "chunk-PB3AIZ72": {"file": "chunk-PB3AIZ72.js"}, "chunk-4RGNMPSI": {"file": "chunk-4RGNMPSI.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}