import React from 'react';
import { ShoppingCart } from 'lucide-react';
import { Button } from '@ui/button';
import { Badge } from '@ui/badge';
import { useCart } from '@kivu-smartfarm/shared';

const CartIcon = () => {
  const { getTotalItems, toggleCart } = useCart();
  const itemCount = getTotalItems();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleCart}
      className="relative p-2 hover:bg-green-50 transition-colors"
    >
      <ShoppingCart className="w-5 h-5 text-gray-700" />
      {itemCount > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-green-600 hover:bg-green-600"
        >
          {itemCount > 99 ? '99+' : itemCount}
        </Badge>
      )}
    </Button>
  );
};

export default CartIcon;
