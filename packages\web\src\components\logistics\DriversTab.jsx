import React from 'react';
import { motion } from 'framer-motion';
import { Phone } from 'lucide-react';
import { Button } from '@ui/button';
import { Badge } from '@ui/badge';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '../ui/dialog';
import { Input } from '../ui/input';
import { useLanguage } from '@kivu-smartfarm/shared';

const driversData = [
	{
		id: 1,
		name: '<PERSON>',
		phone: '+243 123 456 789',
		vehicle: 'Toyota Hiace',
		plateNumber: 'CD 1234 AB',
		status: 'active',
		currentLocation: 'En route to Bukavu',
		rating: 4.8,
		completedDeliveries: 156,
	},
	{
		id: 2,
		name: '<PERSON>',
		phone: '+243 987 654 321',
		vehicle: 'Isuzu NPR',
		plateNumber: 'CD 5678 CD',
		status: 'available',
		currentLocation: 'Goma Depot',
		rating: 4.9,
		completedDeliveries: 203,
	},
	{
		id: 3,
		name: '<PERSON>',
		phone: '+243 555 123 456',
		vehicle: 'Mitsubishi Canter',
		plateNumber: 'CD 9012 EF',
		status: 'maintenance',
		currentLocation: 'Service Center',
		rating: 4.7,
		completedDeliveries: 89,
	},
];

const DriversTab = () => {
	const { t } = useLanguage();
	const [addDriverOpen, setAddDriverOpen] = React.useState(false);
	const [contactDriver, setContactDriver] = React.useState(null);
	const [assignDriver, setAssignDriver] = React.useState(null);
	const [newDriver, setNewDriver] = React.useState({ name: '', phone: '', vehicle: '', plateNumber: '' });

	const getDriverStatusColor = (status) => {
		switch (status) {
			case 'active':
				return 'bg-green-500';
			case 'available':
				return 'bg-blue-500';
			case 'maintenance':
				return 'bg-orange-500';
			case 'offline':
				return 'bg-gray-500';
			default:
				return 'bg-gray-500';
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, x: 20 }}
			animate={{ opacity: 1, x: 0 }}
			className="space-y-6"
		>
			<div className="flex flex-col sm:flex-row justify-between items-center">
				<h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-0">
					{t('logistics.drivers.title', 'Driver Management')}
				</h2>
				<Button
					className="bg-gradient-to-r from-green-600 to-emerald-600 w-full sm:w-auto"
					onClick={() => setAddDriverOpen(true)}
				>
					{t('logistics.drivers.add', 'Add New Driver')}
				</Button>
				<Dialog open={addDriverOpen} onOpenChange={setAddDriverOpen}>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Add New Driver</DialogTitle>
						</DialogHeader>
						<form onSubmit={e => { e.preventDefault(); setAddDriverOpen(false); }} className="space-y-4">
							<Input placeholder="Name" value={newDriver.name} onChange={e => setNewDriver({ ...newDriver, name: e.target.value })} required />
							<Input placeholder="Phone" value={newDriver.phone} onChange={e => setNewDriver({ ...newDriver, phone: e.target.value })} required />
							<Input placeholder="Vehicle" value={newDriver.vehicle} onChange={e => setNewDriver({ ...newDriver, vehicle: e.target.value })} required />
							<Input placeholder="Plate Number" value={newDriver.plateNumber} onChange={e => setNewDriver({ ...newDriver, plateNumber: e.target.value })} required />
							<DialogFooter>
								<Button type="submit" className="gradient-bg">Add Driver</Button>
							</DialogFooter>
						</form>
					</DialogContent>
				</Dialog>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{driversData.map((driver) => (
					<motion.div
						key={driver.id}
						whileHover={{ scale: 1.05 }}
						className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border-0 p-6"
					>
						<div className="flex justify-between items-start mb-4">
							<div>
								<h3 className="text-md sm:text-lg font-bold text-gray-800">
									{driver.name}
								</h3>
								<p className="text-xs text-gray-600 flex items-center">
									<Phone className="w-3 h-3 mr-1" />
									{driver.phone}
								</p>
							</div>
							<Badge
								className={`${
									getDriverStatusColor(driver.status)
								} text-white text-xs`}
							>
								{t(
									`logistics.driver_status.${driver.status}`,
									driver.status
								)}
							</Badge>
						</div>

						<div className="space-y-2 mb-4 text-xs sm:text-sm">
							<p className="text-gray-600">
								<span className="font-medium">
									{t('logistics.vehicle', 'Vehicle')}:
								</span>{' '}
								{driver.vehicle}
							</p>
							<p className="text-gray-600">
								<span className="font-medium">
									{t('logistics.plate', 'Plate')}:
								</span>{' '}
								{driver.plateNumber}
							</p>
							<p className="text-gray-600">
								<span className="font-medium">
									{t('logistics.location', 'Location')}:
								</span>{' '}
								{driver.currentLocation}
							</p>
							<p className="text-gray-600">
								<span className="font-medium">
									{t('logistics.rating', 'Rating')}:
								</span>{' '}
								⭐ {driver.rating}
							</p>
							<p className="text-gray-600">
								<span className="font-medium">
									{t('logistics.deliveries', 'Deliveries')}:
								</span>{' '}
								{driver.completedDeliveries}
							</p>
						</div>

						<div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
							<Button
								variant="outline"
								size="sm"
								className="flex-1 text-xs"
								onClick={() => setContactDriver(driver)}
							>
								{t('logistics.contact', 'Contact')}
							</Button>
							<Button
								size="sm"
								className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 text-xs"
								onClick={() => setAssignDriver(driver)}
							>
								{t('logistics.assign', 'Assign')}
							</Button>
						</div>
					</motion.div>
				))}
			</div>

			<Dialog open={!!contactDriver} onOpenChange={open => !open && setContactDriver(null)}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Contact Driver</DialogTitle>
					</DialogHeader>
					{contactDriver && (
						<div className="space-y-2">
							<div><span className="font-medium">Name:</span> {contactDriver.name}</div>
							<div><span className="font-medium">Phone:</span> {contactDriver.phone}</div>
							<div><span className="font-medium">Vehicle:</span> {contactDriver.vehicle}</div>
							<div><span className="font-medium">Plate:</span> {contactDriver.plateNumber}</div>
						</div>
					)}
					<DialogFooter>
						<Button onClick={() => setContactDriver(null)} className="gradient-bg">Close</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
			<Dialog open={!!assignDriver} onOpenChange={open => !open && setAssignDriver(null)}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Assign Driver</DialogTitle>
					</DialogHeader>
					{assignDriver && (
						<form onSubmit={e => { e.preventDefault(); setAssignDriver(null); }} className="space-y-4">
							<DialogDescription>Assign <span className="font-semibold">{assignDriver.name}</span> to a shipment.</DialogDescription>
							<Input placeholder="Order/Shipment ID" required />
							<DialogFooter>
								<Button type="submit" className="gradient-bg">Assign</Button>
							</DialogFooter>
						</form>
					)}
				</DialogContent>
			</Dialog>
		</motion.div>
	);
};

export default DriversTab;
