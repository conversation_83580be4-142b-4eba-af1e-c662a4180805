import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { ArrowLeft } from 'lucide-react';
import Navigation from '../components/Navigation';

// This is a placeholder. In a real app, fetch demand details from API or context.
const mockDemands = [
  {
    id: 'D001',
    buyerName: 'Kivu Grand Hotel',
    buyerType: 'Hotel',
    productNeeded: 'Fresh Tomatoes',
    quantity: '500 kg',
    qualitySpec: 'Grade A, Organic',
    deliveryLocation: 'Goma, North Kivu',
    deliveryDate: '2025-06-15',
    contactPerson: 'Mr. <PERSON>',
    contactPhone: '+243 812 345 678',
    postedDate: '2025-06-01',
    status: 'Open'
  },
  // ...add more mock demands as needed
];

export default function DemandDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const demand = mockDemands.find(d => d.id === id);

  if (!demand) return <div className="p-8">Demand not found.</div>;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <Navigation />
      <div className="max-w-xl mx-auto pt-24 px-4">
        <Button variant="ghost" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" /> Back
        </Button>
        <Card>
          <CardHeader>
            <CardTitle>{demand.productNeeded}</CardTitle>
            <CardDescription>Demand ID: {demand.id}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><span className="font-medium">Buyer:</span> {demand.buyerName} ({demand.buyerType})</div>
            <div><span className="font-medium">Quantity:</span> {demand.quantity}</div>
            <div><span className="font-medium">Quality Spec:</span> {demand.qualitySpec}</div>
            <div><span className="font-medium">Delivery Location:</span> {demand.deliveryLocation}</div>
            <div><span className="font-medium">Needed By:</span> {demand.deliveryDate}</div>
            <div><span className="font-medium">Contact:</span> {demand.contactPerson} ({demand.contactPhone})</div>
            <div><span className="font-medium">Posted On:</span> {demand.postedDate}</div>
            <div><Badge>{demand.status}</Badge></div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
