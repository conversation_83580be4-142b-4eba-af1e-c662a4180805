{"lastValidatedTimestamp": 1749295056729, "projects": {"C:\\My Software Projects\\kivusmartfarm": {"name": "kivu-smartfarm", "version": "0.0.0"}, "C:\\My Software Projects\\kivusmartfarm\\packages\\shared": {"name": "@kivu-smartfarm/shared", "version": "0.0.0"}, "C:\\My Software Projects\\kivusmartfarm\\packages\\web": {"name": "@kivu-smartfarm/web", "version": "0.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": true}